"use client";
import React from "react";
import ArticleCard, { ArticleCardProps } from "./ui/ArticleCard";
import SecondaryBtn from "./ui/SecondaryBtn";
import { useDictionary } from '@/lib/DictionaryContext';

const LatestArticles = () => {
    const dictionary = useDictionary(); // Get the dictionary content from the context
    const articles = dictionary.articleCard;

    // Get the article header content
    const articleHeader = dictionary.articleTitleAndButton;

    // Check if the dictionary content is available
    if (!articleHeader || !articles) return null;

    return (
        <div className="lg:px-28 md:px-12 px-4 py-12 lg:py-20 md:py-16">
            <div className="flex flex-col gap-3">

                {/* Title and See All Button */}
                <div className="flex flex-row justify-between">
                    <h3 className="text-left text-default-900 lg:text-4xl md:text-4xl text-3xl font-semibold font-['Poppins'] leading-[60px]">
                        {articleHeader.title}
                    </h3>
                    <SecondaryBtn text={articleHeader.seeAllButtonText} color="text-default-900" />
                    
                </div>

                {/* Articles */}
                <div className="overflow-hidden lg:overflow-x-auto">
                    <div className="flex flex-col gap-6 md:flex-row md:gap-6 lg:gap-6">
                        {Object.values(articles).map((article, index) => ( // Map through the articles object and extract the values as an array
                            <div key={article.id} className={`${index >= 3 ? 'hidden lg:block' : index >= 2 ? 'md:hidden lg:block' : '' }`}>
                                <ArticleCard key={article.id} {...article} />
                                
                            </div>
                        ))}
                    </div>  
                </div>
            </div>
        </div>
    )
}

export default LatestArticles;
//end