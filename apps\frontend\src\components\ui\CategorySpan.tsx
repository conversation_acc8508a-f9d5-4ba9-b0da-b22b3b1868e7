const CategorySpan = ({ 
    categories,
    maxVisible = categories.length
}: { 
    categories: string[],
    maxVisible?: number
}) => {
    const hasMore = categories.length > maxVisible;

  return (
    <div className="flex flex-wrap gap-2">
        {categories.map((category, index) => (
            index < maxVisible && (
                <div key={index} className="self-start w-fit inline-flex px-3 py-1 bg-default-100 hover:bg-default-200 rounded-full transition-colors duration-200 whitespace-nowrap">
                    <span className="text-sm text-default-600">
                        {category}
                    </span>
                </div>
            )
        ))}
        {hasMore && (
            <div className="self-start w-fit inline-flex px-3 py-1 bg-default-100 hover:bg-default-200 rounded-full transition-colors duration-200 whitespace-nowrap">
                <span className="text-sm text-default-600">
                    +{categories.length - maxVisible}
                </span>
            </div>
        )}
    </div>

  );
};

export default CategorySpan;
//end