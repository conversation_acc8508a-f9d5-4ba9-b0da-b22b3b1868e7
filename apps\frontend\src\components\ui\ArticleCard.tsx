"use client"
import Image from "next/image";
import ProfileBio, { ProfileBioProps } from "./ProfileBio";
import Link from "next/link";
import CategorySpan from "./CategorySpan";

export interface ArticleCardProps {
    id: number;
    imageUrl: string;
    imageAlt: string;
    date: string;
    title: string;
    description: string;
    categories: string[];
    author: ProfileBioProps; // Use the ProfileBioProps interface for the author
}

const ArticleCard = ({ imageUrl, imageAlt, date, title, description, 
                        categories, author}: ArticleCardProps) => {
    const formattedDate = new Date(date + "T12:00:00").toLocaleDateString("en-US", {
        month: "long",
        day: "numeric",
        year: "numeric",
    });

    return (
        // <div className="w-full max-w-[500px] mx-auto lg:w-[500px] md:w-[390px] lg:flex-shrink-0 lg:mx-0 
        //                 flex flex-col h-full bg-white rounded-xl overflow-hidden
        //                 shadow-sm hover:shadow-md transition-shadow duration-200">
        <div className="w-full max-w-[500px] px-4 sm:px-6 md:px-0 mx-auto flex flex-col h-full bg-white 
             rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">

            {/* Image Post */}
            <div className="relative w-full h-[300px] rounded-xl overflow-hidden">
                <Image
                    src={imageUrl}
                    alt={imageAlt}
                    fill
                    className="object-cover"
                />
            </div>
            
            {/* Content */}
            <div className="flex flex-col flex-1 gap-4 p-4">
                {/* Date and Category */}
                <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">
                        {formattedDate}
                    </p>
                </div>

                
                
                {/* Title and Description */}
                <div className="flex flex-col gap-3">
                    <Link href="/" className="group">
                        <h3 className="font-bold text-xl lg:text-2xl text-default-900 line-clamp-2 group-hover:underline transition-all duration-200">
                            {title}
                        </h3>
                    </Link>
                    <p className="text-base text-default-600 line-clamp-3">
                        {description}
                    </p>
                </div>

                <CategorySpan categories={categories} />
                
                
                {/* Author */}
                <div className="mt-auto">
                    <ProfileBio {...author} />
                </div>
            </div>
        </div>
    );

}

export default ArticleCard;
//end